import { NextResponse } from 'next/server';
import { getDynamicTable, validateDatabaseCode } from '@/lib/drizzleTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { db } from '@/lib/db-server';
import { count, eq, or, isNull, isNotNull, ne, desc, and, ilike, inArray } from 'drizzle-orm';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    const database = rawDatabase.toLowerCase();

    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    const config = await getDatabaseConfig(database);
    if (!config) {
      return NextResponse.json(
        { success: false, error: '未找到数据库配置' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const filtersParam = searchParams.get('filters');
    const fieldName = searchParams.get('field');
    const searchContextParam = searchParams.get('searchContext'); // New parameter for search context

    if (!fieldName) {
      return NextResponse.json(
        { success: false, error: '缺少字段名参数' },
        { status: 400 }
      );
    }

    const table = await getDynamicTable(database);

    // 构建完整的搜索参数，包括文本搜索和筛选条件
    const fullSearchParams = new URLSearchParams();

    // Handle search context (current search results)
    let searchContext = null;
    if (searchContextParam) {
      try {
        searchContext = JSON.parse(searchContextParam);
        console.log('[DynamicCounts] 接收到搜索上下文:', searchContext);
      } catch (e) {
        console.error("解析搜索上下文失败:", e);
      }
    }

    if (filtersParam) {
      try {
        const parsedFilters = JSON.parse(filtersParam);

        Object.entries(parsedFilters).forEach(([key, value]) => {
          if (key !== fieldName && value !== null && value !== undefined && value !== '') {
            if (Array.isArray(value)) {
              // 对于数组值，需要特殊处理
              value.forEach(v => fullSearchParams.append(key, String(v)));
            } else {
              fullSearchParams.set(key, String(value));
            }
          }
        });
      } catch (_e) {
        console.error("解析筛选条件失败:", _e);
      }
    }

    // 手动构建where条件，与主搜索逻辑保持一致
    const whereConditions: any[] = [];

    // 处理全局搜索关键词 (allFields)
    const globalKeyword = fullSearchParams.get('allFields');

    if (globalKeyword && globalKeyword.trim()) {
      const keyword = globalKeyword.trim();
      // 只在文本字段中进行搜索，排除数字字段
      const searchableFields = config.fields.filter(f =>
        f.isSearchable &&
        f.searchType === 'contains' &&
        f.fieldType === 'text'  // 只搜索文本字段
      );

      console.log(`[DynamicCounts] 全局搜索关键词: "${keyword}"`);
      console.log(`[DynamicCounts] 可搜索字段数量: ${searchableFields.length}`);
      console.log(`[DynamicCounts] 可搜索字段:`, searchableFields.map(f => `${f.fieldName}(${f.fieldType})`));

      if (searchableFields.length > 0) {
        const globalSearchConditions = searchableFields
          .map(f => {
            const column = table[f.fieldName];
            console.log(`[DynamicCounts] 字段 ${f.fieldName} 的列对象:`, column ? '存在' : '不存在');
            return column ? ilike(column, `%${keyword}%`) : null;
          })
          .filter(Boolean);

        console.log(`[DynamicCounts] 有效的搜索条件数量: ${globalSearchConditions.length}`);

        if (globalSearchConditions.length > 0) {
          whereConditions.push(or(...globalSearchConditions)!);
        }
      }
    }

    // 处理其他筛选条件
    fullSearchParams.forEach((value, key) => {
      if (!['page', 'limit', 'sortBy', 'sortOrder', 'allFields'].includes(key)) {
        const fieldConfig = config.fields.find(f => f.fieldName === key);
        if (fieldConfig) {
          const column = table[key];
          if (column && value) {
            // 处理文本搜索字段
            if (fieldConfig.searchType === 'contains') {
              whereConditions.push(ilike(column, `%${value}%`));
            }
            // 处理多选字段（数组值）
            else if (Array.isArray(value)) {
              // 检查是否包含 N/A 值
              const hasNullValue = value.includes('N/A');
              if (hasNullValue) {
                const nonNullValues = value.filter(v => v !== 'N/A');
                if (nonNullValues.length > 0) {
                  whereConditions.push(
                    or(
                      inArray(column, nonNullValues),
                      isNull(column),
                      eq(column, '')
                    )
                  );
                } else {
                  whereConditions.push(
                    or(
                      isNull(column),
                      eq(column, '')
                    )
                  );
                }
              } else {
                // 对于多个值，使用 inArray
                whereConditions.push(inArray(column, value));
              }
            }
            // 处理单个值
            else {
              if (value === 'N/A') {
                whereConditions.push(
                  or(
                    isNull(column),
                    eq(column, '')
                  )
                );
              } else {
                whereConditions.push(eq(column, value));
              }
            }
          }
        }
      }
    });

    // 组合所有条件
    const baseWhere = whereConditions.length > 0
      ? (whereConditions.length === 1 ? whereConditions[0] : and(...whereConditions))
      : undefined;

    console.log(`[DynamicCounts] 构建的where条件:`, baseWhere);
    console.log(`[DynamicCounts] 搜索参数:`, Object.fromEntries(fullSearchParams.entries()));

    // 获取该字段的所有唯一值及其计数
    const fieldConfig = config.fields.find(f => f.fieldName === fieldName);
    if (!fieldConfig) {
      return NextResponse.json(
        { success: false, error: '字段配置未找到' },
        { status: 404 }
      );
    }

    try {
      // 获取字段列对象
      const fieldColumn = table[fieldName];
      if (!fieldColumn) {
        return NextResponse.json(
          { success: false, error: `字段 ${fieldName} 在表中不存在` },
          { status: 404 }
        );
      }

      // 1. 获取非空值的计数 - 使用 Drizzle 语法
      // 修复：简化where条件组合，避免复杂的嵌套
      let nonNullWhere;

      // If we have search filters, apply them directly instead of using search context
      if (baseWhere) {
        nonNullWhere = and(
          baseWhere,
          ne(fieldColumn, ''),
          isNotNull(fieldColumn)
        );
      } else {
        // Original logic when no search context
        if (baseWhere) {
          nonNullWhere = and(
            baseWhere,
            ne(fieldColumn, ''),
            isNotNull(fieldColumn)
          );
        } else {
          nonNullWhere = and(
            ne(fieldColumn, ''),
            isNotNull(fieldColumn)
          );
        }
      }

      console.log(`[DynamicCounts] 执行查询，字段: ${fieldName}`);
      console.log(`[DynamicCounts] 应用搜索条件: ${baseWhere ? 'Yes' : 'No'}`);
      if (baseWhere) {
        console.log(`[DynamicCounts] 搜索参数:`, filtersParam ? JSON.parse(filtersParam) : {});
      }

      const groupedValues = await db
        .select({
          value: fieldColumn,
          count: count()
        })
        .from(table)
        .where(nonNullWhere)
        .groupBy(fieldColumn)
        .orderBy(desc(count()));

      console.log(`[DynamicCounts] 查询结果数量: ${groupedValues.length}`);
      if (groupedValues.length > 0) {
        console.log(`[DynamicCounts] 前几个结果:`, groupedValues.slice(0, 3));
      }

      // 2. 单独统计空值（null和空字符串）
      const nullWhere = baseWhere
        ? and(baseWhere, or(isNull(fieldColumn), eq(fieldColumn, '')))
        : or(isNull(fieldColumn), eq(fieldColumn, ''));

      const nullCountResult = await db
        .select({ count: count() })
        .from(table)
        .where(nullWhere);

      const nullCount = Number(nullCountResult[0]?.count || 0);
      console.log(`[DynamicCounts] 空值数量: ${nullCount}`);

      // 3. 合并非空值和空值的结果
      const nonNullCounts = groupedValues.map((item) => ({
        value: String(item.value).trim(),
        count: Number(item.count),
        isNull: false
      }));

      // 4. 添加N/A项（如果有空值）
      const counts = [...nonNullCounts];
      if (nullCount > 0) {
        counts.push({
          value: 'N/A',
          count: nullCount,
          isNull: true
        });
      }

      // 5. 排序：按计数降序，N/A项排在最后
      counts.sort((a, b) => {
        if (a.isNull && !b.isNull) return 1;
        if (!a.isNull && b.isNull) return -1;
        return b.count - a.count; // 按计数降序排列
      });

      // 6. 移除内部使用的isNull字段
      const finalCounts = counts.map((item) => ({
        value: item.value,
        count: item.count
      }));

      // 验证总计
      const totalCount = finalCounts.reduce((sum, item) => sum + item.count, 0);
      console.log(`[DynamicCounts] 字段 ${fieldName} 统计完成:`);
      console.log(`[DynamicCounts] - 非空值项目数: ${groupedValues.length}`);
      console.log(`[DynamicCounts] - 空值数量: ${nullCount}`);
      console.log(`[DynamicCounts] - 总项目数: ${finalCounts.length}`);
      console.log(`[DynamicCounts] - 总记录数: ${totalCount}`);
      if (searchContext && searchContext.resultIds) {
        console.log(`[DynamicCounts] - 预期记录数: ${searchContext.resultIds.length}`);
        console.log(`[DynamicCounts] - 数据完整性: ${totalCount === searchContext.resultIds.length ? '✓' : '✗'}`);
      }

      return NextResponse.json({
        success: true,
        data: finalCounts,
        field: fieldName,
        debug: {
          totalCount,
          expectedCount: searchContext?.resultIds?.length || 'N/A',
          nonNullItems: groupedValues.length,
          nullCount
        }
      });
    } catch (__error) {
      console.error(`Failed to fetch dynamic counts:`, __error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch dynamic counts' },
        { status: 500 }
      );
    }
  } catch (__error) {
    console.error(`Dynamic counts API error:`, __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 