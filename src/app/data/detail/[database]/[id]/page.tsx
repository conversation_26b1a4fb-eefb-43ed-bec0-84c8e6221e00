import Navigation from "@/components/Navigation";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { validateDatabaseCode } from "@/lib/dynamicTableMapping";
import { getDatabaseAccessLevel } from "@/lib/permissions";
import { checkPermissions } from "@/lib/server/permissions";
import { getDatabaseConfig } from "@/lib/configCache";
import { notFound } from "next/navigation";
import { formatDetailDate } from "@/lib/utils";
import DetailPageBreadcrumb from "./DetailPageBreadcrumb";

interface DetailPageProps {
  params: Promise<{ database: string; id: string }>;
}

export default async function DetailPage({ params }: DetailPageProps) {
  const { database, id } = await params;

  // 输入验证
  if (!id || typeof id !== 'string') {
    notFound();
  }

  // 验证数据库代码
  const validation = await validateDatabaseCode(database);
  if (!validation.isValid) {
    notFound();
  }

  // 获取动态模型
  let model;
  try {
    model = await getDynamicTable(database);
  } catch (__error) {
    console.error('获取模型失败:', __error);
    notFound();
  }

  if (!isDrizzleTable(model)) {
    notFound();
  }

  const item = await (model as any).findUnique({
    where: { id: id },
  });

  if (!item) {
    notFound();
  }

  // 权限检查
  const requiredLevel = await getDatabaseAccessLevel(database);
  const hasAccess = await checkPermissions(requiredLevel);
  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation showSearch />
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h1 className="text-xl font-semibold text-red-800 mb-2">访问受限</h1>
            <p className="text-red-600">
              您没有权限访问此数据库。请升级您的会员等级以获得访问权限。
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 获取数据库配置以获取字段信息
  const config = await getDatabaseConfig(database);
  const detailFields = config.fields
    .filter(f => (f as any).isActive !== false && ((f as any).isVisible || (f as any).detailOrder > 0))
    .sort((a, b) => (a as any).detailOrder - (b as any).detailOrder);

  // 将item转换为any类型以便访问动态属性
  const data = item as any;

  // 获取主要显示字段（通常是第一个字段作为标题）
  const titleField = detailFields[0];
  const title = titleField ? data[titleField.fieldName] || 'Unknown' : 'Detail View';

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation showSearch />
      <DetailPageBreadcrumb database={database} itemTitle={title} />
      <div className="container mx-auto px-4 py-8">

        <div className="space-y-6">
          {/* 主要信息卡片 */}
          <Card className="shadow-sm">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
              <CardTitle className="flex items-center justify-between text-lg">
                <span className="text-gray-800">{title}</span>
                <Badge variant="outline" className="bg-white text-blue-600 border-blue-200">{database}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-hidden">
                <table className="w-full border-collapse bg-white">
                  <tbody>
                    {detailFields.map((field, _index) => {
                      const value = data[field.fieldName];
                      if (!value && value !== 0 && value !== false) return null;

                      // 格式化显示值
                      let displayValue = String(value);
                      if (field.fieldType ==="date" && value) {
                        displayValue = formatDetailDate(value);
                      } else if (field.fieldType === 'boolean') {
                        displayValue = value ? 'Yes' : 'No';
                      }

                      // 判断是否为长文本
                      const isLongText = String(value).length > 100;

                      return (
                        <tr key={field.fieldName} className="border-b border-gray-100 last:border-b-0 hover:bg-blue-25 transition-colors duration-150">
                          <td className="px-5 py-4 text-sm font-medium text-gray-700 bg-gray-50/80 border-r border-gray-200 align-top" style={{width: '200px', minWidth: '200px', maxWidth: '200px'}}>
                            <div className="break-words">
                              {field.displayName}
                            </div>
                          </td>
                          <td className="px-5 py-4 text-sm text-gray-900 align-top">
                            <div className={`${isLongText ? 'whitespace-pre-wrap leading-relaxed text-gray-800' : 'break-words'} max-w-none`}>
                              {displayValue}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>


        </div>
      </div>
    </div>
  );
}
