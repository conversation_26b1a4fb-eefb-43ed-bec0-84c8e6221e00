/**
 * Enhanced Database Configuration Service for Drizzle ORM
 * 
 * This service provides optimized configuration management with:
 * - Removed Prisma dependencies
 * - Enhanced caching strategies
 * - Performance monitoring
 * - Type-safe configuration handling
 */

import Redis from 'ioredis';
import { db } from '../drizzle';
import { databaseConfigs, fieldConfigs } from '@/db/schema';
import { eq } from 'drizzle-orm';
import type {
  EnhancedDatabaseConfig,
  ConfigValidationResult,
  DatabasePerformanceMetrics,
  IDatabaseConfigService,
  ConfigCacheEntry,
  DEFAULT_CONFIG,
  DEFAULT_EXPORT_CONFIG
} from '../types/databaseConfig';

// Redis client for caching
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Cache configuration
const CACHE_PREFIX = 'enhanced_db_config:';
const METRICS_PREFIX = 'db_metrics:';
const CACHE_TTL = 300; // 5 minutes
const METRICS_TTL = 60; // 1 minute

/**
 * Enhanced Database Configuration Service
 */
export class EnhancedDatabaseConfigService implements IDatabaseConfigService {
  private static instance: EnhancedDatabaseConfigService;
  private configCache = new Map<string, ConfigCacheEntry>();
  private metricsCache = new Map<string, DatabasePerformanceMetrics>();

  private constructor() {}

  static getInstance(): EnhancedDatabaseConfigService {
    if (!this.instance) {
      this.instance = new EnhancedDatabaseConfigService();
    }
    return this.instance;
  }

  /**
   * Get enhanced database configuration with optimized caching
   */
  async getConfig(databaseCode: string): Promise<EnhancedDatabaseConfig> {
    const cacheKey = `${CACHE_PREFIX}${databaseCode}`;
    
    try {
      // Check memory cache first
      const memoryCache = this.configCache.get(databaseCode);
      if (memoryCache && Date.now() < memoryCache.expiresAt) {
        return memoryCache.config;
      }

      // Check Redis cache
      const cached = await redis.get(cacheKey);
      if (cached) {
        const config = JSON.parse(cached) as EnhancedDatabaseConfig;
        this.updateMemoryCache(databaseCode, config);
        return config;
      }
    } catch (error) {
      console.warn('Cache read failed, fetching from database:', error);
    }

    // Fetch from database
    const config = await this.fetchConfigFromDatabase(databaseCode);
    
    // Update caches
    await this.updateCaches(databaseCode, config);
    
    return config;
  }

  /**
   * Fetch configuration from database with enhanced fields
   */
  private async fetchConfigFromDatabase(databaseCode: string): Promise<EnhancedDatabaseConfig> {
    const configResult = await db
      .select({
        id: databaseConfigs.id,
        code: databaseConfigs.code,
        name: databaseConfigs.name,
        category: databaseConfigs.category,
        description: databaseConfigs.description,
        accessLevel: databaseConfigs.accessLevel,
        tableName: databaseConfigs.tableName,
        schemaName: databaseConfigs.schemaName,
        connectionPool: databaseConfigs.connectionPool,
        queryTimeout: databaseConfigs.queryTimeout,
        cacheStrategy: databaseConfigs.cacheStrategy,
        cacheTTL: databaseConfigs.cacheTTL,
        maxExportLimit: databaseConfigs.maxExportLimit,
        defaultExportLimit: databaseConfigs.defaultExportLimit,
        exportFormats: databaseConfigs.exportFormats,
        isActive: databaseConfigs.isActive,
        sortOrder: databaseConfigs.sortOrder,
        createdAt: databaseConfigs.createdAt,
        updatedAt: databaseConfigs.updatedAt,
        defaultSort: databaseConfigs.defaultSort,
        exportConfig: databaseConfigs.exportConfig,
        indexStrategy: databaseConfigs.indexStrategy,
      })
      .from(databaseConfigs)
      .where(eq(databaseConfigs.code, databaseCode))
      .limit(1);

    if (!configResult.length) {
      throw new Error(`Database configuration not found: ${databaseCode}`);
    }

    const dbConfig = configResult[0];

    // Build enhanced configuration with defaults
    const enhancedConfig: EnhancedDatabaseConfig = {
      id: dbConfig.id,
      code: dbConfig.code,
      name: dbConfig.name,
      category: dbConfig.category,
      description: dbConfig.description || undefined,
      accessLevel: (dbConfig.accessLevel as any) || DEFAULT_CONFIG.accessLevel!,
      tableName: dbConfig.tableName || dbConfig.code,
      schemaName: (dbConfig.schemaName as any) || DEFAULT_CONFIG.schemaName!,
      connectionPool: (dbConfig.connectionPool as any) || DEFAULT_CONFIG.connectionPool!,
      queryTimeout: dbConfig.queryTimeout || DEFAULT_CONFIG.queryTimeout!,
      cacheStrategy: (dbConfig.cacheStrategy as any) || DEFAULT_CONFIG.cacheStrategy!,
      cacheTTL: dbConfig.cacheTTL || DEFAULT_CONFIG.cacheTTL!,
      indexStrategy: dbConfig.indexStrategy as any,
      exportConfig: (dbConfig.exportConfig as any) || DEFAULT_EXPORT_CONFIG,
      exportFormats: (dbConfig.exportFormats as any) || DEFAULT_CONFIG.exportFormats!,
      maxExportLimit: dbConfig.maxExportLimit,
      defaultExportLimit: dbConfig.defaultExportLimit,
      defaultSort: dbConfig.defaultSort as any,
      isActive: dbConfig.isActive,
      sortOrder: dbConfig.sortOrder,
      createdAt: dbConfig.createdAt,
      updatedAt: dbConfig.updatedAt,
    };

    return enhancedConfig;
  }

  /**
   * Validate configuration with comprehensive checks
   */
  validateConfig(config: Partial<EnhancedDatabaseConfig>): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields validation
    if (!config.code) errors.push('Database code is required');
    if (!config.name) errors.push('Database name is required');
    if (!config.category) errors.push('Database category is required');
    if (!config.tableName) errors.push('Table name is required');

    // Access level validation
    if (config.accessLevel && !['free', 'premium', 'enterprise'].includes(config.accessLevel)) {
      errors.push('Invalid access level');
    }

    // Performance configuration validation
    if (config.queryTimeout && config.queryTimeout < 1000) {
      warnings.push('Query timeout less than 1 second may cause issues');
    }
    if (config.queryTimeout && config.queryTimeout > 300000) {
      warnings.push('Query timeout greater than 5 minutes may cause timeouts');
    }

    // Cache configuration validation
    if (config.cacheTTL && config.cacheTTL < 60) {
      warnings.push('Cache TTL less than 1 minute may cause excessive database load');
    }

    // Export limits validation
    if (config.maxExportLimit && config.defaultExportLimit && 
        config.defaultExportLimit > config.maxExportLimit) {
      errors.push('Default export limit cannot exceed maximum export limit');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Update configuration with validation
   */
  async updateConfig(databaseCode: string, updates: Partial<EnhancedDatabaseConfig>): Promise<void> {
    // Validate updates
    const validation = this.validateConfig(updates);
    if (!validation.isValid) {
      throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
    }

    // Update database
    await db
      .update(databaseConfigs)
      .set({
        ...updates,
        updatedAt: new Date(),
      } as any)
      .where(eq(databaseConfigs.code, databaseCode));

    // Clear caches
    await this.clearCache(databaseCode);
  }

  /**
   * Clear configuration cache
   */
  async clearCache(databaseCode?: string): Promise<void> {
    if (databaseCode) {
      // Clear specific cache
      this.configCache.delete(databaseCode);
      this.metricsCache.delete(databaseCode);
      
      try {
        await redis.del(`${CACHE_PREFIX}${databaseCode}`);
        await redis.del(`${METRICS_PREFIX}${databaseCode}`);
      } catch (error) {
        console.warn('Redis cache clear failed:', error);
      }
    } else {
      // Clear all caches
      this.configCache.clear();
      this.metricsCache.clear();
      
      try {
        const keys = await redis.keys(`${CACHE_PREFIX}*`);
        const metricsKeys = await redis.keys(`${METRICS_PREFIX}*`);
        if (keys.length > 0) await redis.del(...keys);
        if (metricsKeys.length > 0) await redis.del(...metricsKeys);
      } catch (error) {
        console.warn('Redis cache clear failed:', error);
      }
    }
  }

  /**
   * Get performance metrics for a database
   */
  async getMetrics(databaseCode: string): Promise<DatabasePerformanceMetrics> {
    // Check cache first
    const cached = this.metricsCache.get(databaseCode);
    if (cached && Date.now() - cached.lastUpdated.getTime() < METRICS_TTL * 1000) {
      return cached;
    }

    // For now, return mock metrics - in production, this would fetch real metrics
    const metrics: DatabasePerformanceMetrics = {
      avgQueryTime: Math.random() * 100 + 50, // 50-150ms
      totalQueries: Math.floor(Math.random() * 10000),
      cacheHitRate: Math.random() * 0.3 + 0.7, // 70-100%
      errorRate: Math.random() * 0.05, // 0-5%
      lastUpdated: new Date(),
    };

    // Cache metrics
    this.metricsCache.set(databaseCode, metrics);
    
    try {
      await redis.setex(`${METRICS_PREFIX}${databaseCode}`, METRICS_TTL, JSON.stringify(metrics));
    } catch (error) {
      console.warn('Metrics cache write failed:', error);
    }

    return metrics;
  }

  /**
   * Update memory cache
   */
  private updateMemoryCache(databaseCode: string, config: EnhancedDatabaseConfig): void {
    this.configCache.set(databaseCode, {
      config,
      cachedAt: Date.now(),
      expiresAt: Date.now() + (CACHE_TTL * 1000),
    });
  }

  /**
   * Update both memory and Redis caches
   */
  private async updateCaches(databaseCode: string, config: EnhancedDatabaseConfig): Promise<void> {
    // Update memory cache
    this.updateMemoryCache(databaseCode, config);

    // Update Redis cache
    try {
      await redis.setex(`${CACHE_PREFIX}${databaseCode}`, CACHE_TTL, JSON.stringify(config));
    } catch (error) {
      console.warn('Redis cache write failed:', error);
    }
  }
}

// Export singleton instance
export const enhancedConfigService = EnhancedDatabaseConfigService.getInstance();

// Backward compatibility function
export async function getEnhancedDatabaseConfig(databaseCode: string): Promise<EnhancedDatabaseConfig> {
  return enhancedConfigService.getConfig(databaseCode);
}
